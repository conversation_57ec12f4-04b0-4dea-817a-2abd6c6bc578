#!/usr/bin/env python3
"""
BUG规范性校验主动拉取脚本

该脚本支持通过时间范围参数主动从TAPD平台获取缺陷数据，
并对获取到的缺陷执行现有的BUG规范性评估流程。
同时支持群聊通知功能，将未通过的缺陷以卡片形式发送到群聊中。

使用示例：
    python pull_bugs_evaluation.py --start-time "2024-01-01" --end-time "2024-01-02"
    python pull_bugs_evaluation.py --start-time "2024-01-01 09:00:00" --end-time "2024-01-01 18:00:00" --workspace-id 20375472
    python pull_bugs_evaluation.py --notify-only --start-time "2024-01-01" --end-time "2024-01-02"  # 仅发送群聊通知
"""

import os
import sys
import json
import argparse
import asyncio
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import signal
from backend.app.utils.tapd import md_with_fix

# 添加项目根目录到 Python 路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../../')))

# 导入配置文件（会自动加载环境变量）
from backend.app.config import config
from backend.app.config.config import BUG_EVALUATION_SKIP_MODE, DESCRIPTION_MAX_LENGTH
from backend.app.utils.tapd import tap_client
from backend.app.handlers.handlers import check_bug_data, save_data, get_rules
from backend.app.utils.logger_util import logger
from backend.app.database.database import get_db
from backend.app.service.bug_evaluation_service import check_bug_evaluation_exists, \
    update_bug_evaluation_is_closed_without_push
from backend.app.models.bug import BugEvaluation
from backend.app.service.cards import send_markdown_message
from sqlalchemy.orm import Session
from sqlalchemy import and_
from backend.app.config.config import BUG_EVALUATION_WORKSPACE_ID, MIYING_WORKSPACE_ID, DAOZHEN_WORKSPACE_ID
from backend.app.utils.tapd import parse_description_to_json

class BugPullEvaluator:
    """BUG主动拉取评估器"""

    def __init__(self, workspace_id: str = None, retry_count: int = 3, batch_size: int = 10, skip_mode: str = None,
                 notify_only: bool = False, evaluate_bug: bool = False):
        """
        初始化评估器

        Args:
            workspace_id: 工作区ID，如果不指定则使用配置文件中的默认值
            retry_count: 失败重试次数
            batch_size: 批处理大小（每批处理完后暂停）
            skip_mode: 跳过模式，如果不指定则使用配置文件中的默认值
            notify_only: 是否仅发送群聊通知（不执行评估）
        """
        self.workspace_id = workspace_id or config.BUG_EVALUATION_WORKSPACE_ID
        if not self.workspace_id:
            raise ValueError("未指定工作区ID，请通过参数指定或在配置文件中设置")

        # 设置跳过模式，优先使用参数传入的值，否则使用配置文件中的值
        self.skip_mode = skip_mode or BUG_EVALUATION_SKIP_MODE
        if self.skip_mode not in ['exists', 'passed']:
            logger.warning(f"无效的跳过模式: {self.skip_mode}，将使用默认模式 'exists'")
            self.skip_mode = 'exists'

        # 记录跳过模式信息
        skip_mode_desc = {
            'exists': '记录存在则跳过模式（只要数据库中存在该BUG的评估记录就跳过）',
            'passed': '记录存在且通过则跳过模式（只有当记录存在且评估通过时才跳过）'
        }
        logger.info(f"跳过模式: {self.skip_mode} - {skip_mode_desc.get(self.skip_mode, '未知模式')}")
        self.evaluate_bug = evaluate_bug
        self.notify_only = notify_only
        self.retry_count = retry_count
        self.batch_size = batch_size
        self.processed_count = 0
        self.success_count = 0
        self.failed_count = 0
        self.passed_count = 0
        self.not_passed_count = 0
        self.skipped_count = 0
        self.notified_count = 0  # 群聊通知数量
        self.start_time = None
        self.failed_bugs = []  # 记录失败的缺陷信息
        self.interrupted = False  # 中断标志

        # 注册信号处理器
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _signal_handler(self, signum, _frame):
        """信号处理器，用于优雅退出"""
        logger.info(f"收到信号 {signum}，正在优雅退出...")
        self.interrupted = True

    def _get_failed_bugs_in_time_range(self, start_time: str, end_time: str = None) -> List[Dict]:
        """
        查询指定时间范围内未通过的BUG记录

        Args:
            start_time: 开始时间，格式：'2024-01-01' 或 '2024-01-01 09:00:00'
            end_time: 结束时间，格式：'2024-01-01' 或 '2024-01-01 18:00:00'

        Returns:
            未通过的BUG记录列表
        """
        try:
            # 获取数据库会话
            db_gen = get_db()
            db: Session = next(db_gen)

            try:
                # 解析时间范围
                start_datetime = datetime.strptime(start_time,
                                                   "%Y-%m-%d %H:%M:%S" if len(start_time) > 10 else "%Y-%m-%d")
                if len(start_time) == 10:
                    start_datetime = start_datetime.replace(hour=0, minute=0, second=0, microsecond=0)

                if end_time:
                    end_datetime = datetime.strptime(end_time,
                                                     "%Y-%m-%d %H:%M:%S" if len(end_time) > 10 else "%Y-%m-%d")
                    if len(end_time) == 10:
                        end_datetime = end_datetime.replace(hour=23, minute=59, second=59, microsecond=999999)
                else:
                    end_datetime = datetime.now()

                # 查询未通过的BUG记录
                query = db.query(BugEvaluation).filter(
                    and_(
                        BugEvaluation.workspace_id == str(self.workspace_id),
                        BugEvaluation.overall_passed == False,
                        BugEvaluation.created_at >= start_datetime,
                        BugEvaluation.created_at <= end_datetime,
                        BugEvaluation.desc_fp != True,
                        BugEvaluation.title_fp != True
                    )
                ).order_by(BugEvaluation.evaluated_at.desc())

                failed_bugs = query.all()

                results = []
                for bug in failed_bugs:
                    bug_id = bug.bug_id
                    workspace_id = BUG_EVALUATION_WORKSPACE_ID
                    if workspace_id == DAOZHEN_WORKSPACE_ID or workspace_id == MIYING_WORKSPACE_ID:
                        bug_data = tap_client.get_bug_all_pure_message(workspace_id, bug_id)
                        if bug_data.get('状态', "") == '已关闭':
                            logger.info(f'该bug：{bug_id}已关闭，不发送通知')
                            update_bug_evaluation_is_closed_without_push(workspace_id, str(bug_id), True)
                            continue
                    bug_link = f"https://tapd.woa.com/tapd_fe/{self.workspace_id}/bug/detail/{bug.bug_id}"
                    results.append({
                        "bug_id": bug.bug_id,
                        "title": bug.title or "未知标题",
                        "creator": bug.creator or "未知创建人",
                        "bug_link": bug_link,
                        "evaluated_at": bug.evaluated_at.strftime("%Y-%m-%d %H:%M:%S") if bug.evaluated_at else None
                    })

                logger.info(f"查询到 {len(results)} 个未通过的BUG记录")
                return results

            finally:
                db.close()

        except Exception as e:
            logger.error(f"查询未通过BUG记录失败: {str(e)}")
            return []

    async def _send_failed_bugs_notification(self, start_time: str, failed_bugs: List[Dict]) -> int:
        """
        发送未通过BUG的群聊通知

        Args:
            start_time: 字符串格式的开始时间（如 '2024-01-01 09:00:00'）
            failed_bugs: 未通过的BUG记录列表

        Returns:
            成功发送的通知数量
        """
        if not failed_bugs:
            logger.info("没有未通过的BUG需要发送通知")
            return 0

        success_count = 0
        total_count = len(failed_bugs)

        try:
            # 字符串转 datetime 对象
            parsed_start_time = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            logger.error(f"start_time 格式错误，应为 'YYYY-MM-DD HH:MM:SS'，实际为：{start_time}")
            return 0

        today = datetime.now()
        time_range_str = f"{parsed_start_time.strftime('%Y-%m-%d')} ~ {today.strftime('%Y-%m-%d')}"

        # 构建带日期区间的markdown消息标题
        summary_message = f"### 📅 未通过缺陷规范汇总（{time_range_str}，共 {total_count} 个）\n\n"

        for bug in failed_bugs:
            bug_title = bug.get("title", "未知标题")
            bug_link = bug.get("bug_link", "")
            creator = bug.get("creator", "未知创建人")

            summary_message += f"- [{bug_title}]({bug_link}) <@{creator}>\n"

        try:
            result = await send_markdown_message(summary_message)
            if result.get("errcode") == 0:
                success_count = 1
                logger.info(f"成功发送BUG规范性检查通知，包含 {total_count} 个缺陷")
            else:
                logger.error(f"发送BUG规范性检查通知失败: {result}")
        except Exception as e:
            logger.error(f"发送BUG规范性检查通知异常: {str(e)}")

        return success_count

    async def pull_and_evaluate(self, start_time: str, end_time: str = None, max_bugs: int = None) -> Dict:
        """
        主动拉取并评估缺陷，支持群聊通知功能

        Args:
            start_time: 开始时间，格式：'2024-01-01' 或 '2024-01-01 09:00:00'
            end_time: 结束时间，格式：'2024-01-01' 或 '2024-01-01 18:00:00'
            max_bugs: 最大处理数量

        Returns:
            评估结果统计
        """
        self.start_time = datetime.now()

        if self.notify_only:
            logger.info(f"开始群聊通知任务...")
        if self.evaluate_bug:
            logger.info(f"开始BUG评估任务...")

        logger.info(f"工作区ID: {self.workspace_id}")
        logger.info(f"时间范围: {start_time} ~ {end_time or '现在'}")
        logger.info(f"最大处理数量: {max_bugs or '无限制'}")

        try:
            if self.evaluate_bug:
                # 正常评估模式：获取缺陷并执行评估
                logger.info("正在获取缺陷列表...")
                bugs_data = tap_client.get_all_bugs_by_time_range(
                    workspace_id=self.workspace_id,
                    start_time=start_time,
                    end_time=end_time,
                    max_bugs=max_bugs
                )

                total_bugs = len(bugs_data)
                logger.info(f"获取到 {total_bugs} 个缺陷，开始处理...")

                if total_bugs == 0:
                    logger.info("未找到符合条件的缺陷")
                else:
                    # 批量处理缺陷
                    await self._process_bugs_batch(bugs_data)

            if self.notify_only:
                # 仅发送群聊通知模式：查询数据库中未通过的BUG并发送通知
                logger.info("查询数据库中未通过的BUG记录...")
                end_dt = None
                if end_time:
                    end_dt = datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S") if " " in end_time else datetime.strptime(
                        end_time, "%Y-%m-%d")
                else:
                    end_dt = datetime.now()

                end_time_plus_20min = (end_dt + timedelta(minutes=30)).strftime("%Y-%m-%d %H:%M:%S")
                failed_bugs = self._get_failed_bugs_in_time_range(start_time, end_time_plus_20min)

                if failed_bugs:
                    logger.info(f"找到 {len(failed_bugs)} 个未通过的BUG，发送群聊通知...")
                    self.notified_count = await self._send_failed_bugs_notification(start_time, failed_bugs)
                else:
                    logger.info("未找到需要通知的未通过BUG")
            # 输出最终统计结果
            summary = self._get_summary()
            self._log_summary(summary)

            return summary

        except Exception as e:
            logger.error(f"任务执行失败: {str(e)}")
            import traceback
            traceback.print_exc()
            raise

    async def _people_skip(self, raw_bug) -> bool:
        workspace_id = raw_bug.get('workspace_id', '')
        _, _, people_list = await get_rules(workspace_id)
        people = raw_bug.get('reporter', '')
        if not people or people not in people_list:
            logger.info(f"{people} 不在{workspace_id}的人员配置白名单中:{str(people_list)}")
            return True
        return False

    async def _skip_flag(self, raw_bug, bug_id, bug_title, bug_description) -> bool:
        bug_description_fix = str(parse_description_to_json(bug_description, str(self.workspace_id)))[:DESCRIPTION_MAX_LENGTH]
        if not bug_id:
            logger.warning(f"跳过无效缺陷数据: {json.dumps(raw_bug, ensure_ascii=False, indent=4)}")
            self.skipped_count += 1
            return False
        # 检查数据库记录和评估状态
        exists, is_passed, fix_flag, is_fp = check_bug_evaluation_exists(bug_id, str(self.workspace_id), bug_title,
                                                                         bug_description_fix)
        # 根据配置的跳过模式决定是否跳过
        should_skip = False
        skip_reason = ""

        if self.skip_mode == 'exists':
            # 记录存在则跳过模式：只要存在评估记录就跳过
            if exists:
                should_skip = True
                skip_reason = f"评估记录已存在（通过状态: {is_passed}）"
        elif self.skip_mode == 'passed':
            # 记录存在且通过则跳过模式：只有当记录存在且评估通过时才跳过
            if exists and is_passed:
                should_skip = True
                skip_reason = "评估记录已存在且已通过"
        else:
            # 未知模式，记录警告但不跳过
            logger.warning(f"未知的跳过模式: {self.skip_mode}，将继续处理")
        if not fix_flag and exists:
            should_skip = True
            logger.info("缺陷没有修改直接跳过")
        if is_fp:
            should_skip = True
            logger.info("缺陷已经被标记为误报，跳过处理")
        if should_skip:
            logger.info(f"跳过缺陷 {bug_id}：{skip_reason} [模式: {self.skip_mode}]")
            self.skipped_count += 1
        return should_skip

    async def _process_bugs_batch(self, bugs_data: List[Dict]):
        """批量处理缺陷数据"""
        total_count = len(bugs_data)

        for i, bug_item in enumerate(bugs_data, 1):
            # 检查是否被中断
            if self.interrupted:
                logger.info("检测到中断信号，停止处理")
                break
            try:
                # 提取缺陷基本信息
                raw_bug = bug_item.get("Bug", {})
                bug_id = str(raw_bug.get("id", ""))
                bug_title = raw_bug.get("title", "未知标题")
                bug_description = raw_bug.get('description', '')
                if bug_title and bug_description:
                    if await self._skip_flag(raw_bug, bug_id, bug_title, bug_description):
                        continue
                if await self._people_skip(raw_bug):
                    continue
                logger.info(f"处理缺陷 [{i}/{total_count}]: {bug_id} - {bug_title}")

                # 带重试的处理逻辑
                success = await self._process_single_bug_with_retry(bug_id, bug_title)

                if success:
                    self.success_count += 1
                else:
                    self.failed_count += 1
                    self.failed_bugs.append({
                        "bug_id": bug_id,
                        "bug_title": bug_title,
                        "index": i
                    })

                self.processed_count += 1

                # 批量处理控制和进度显示
                if i % self.batch_size == 0:
                    self._log_progress(i, total_count)
                    logger.info(f"批次处理完成，暂停2秒...")
                    time.sleep(2)
                elif i % 5 == 0:
                    self._log_progress(i, total_count)
                    time.sleep(0.5)
                else:
                    time.sleep(0.3)

            except Exception as e:
                logger.error(f"处理缺陷异常 {bug_item.get('Bug', {}).get('id', 'unknown')}: {str(e)}")
                self.failed_count += 1
                continue

    async def _process_single_bug_with_retry(self, bug_id: str, bug_title: str) -> bool:
        """带重试的单个缺陷处理"""
        for attempt in range(self.retry_count + 1):
            try:
                # 获取完整的缺陷数据
                full_bug_data = tap_client.get_bug_all_pure_message(self.workspace_id, bug_id)

                if not full_bug_data:
                    logger.warning(f"无法获取缺陷详细信息: {bug_id}")
                    if attempt < self.retry_count:
                        logger.info(f"重试获取缺陷数据 ({attempt + 1}/{self.retry_count})")
                        time.sleep(1)
                        continue
                    return False
                if full_bug_data.get('状态', "") == '已关闭' and (
                        BUG_EVALUATION_WORKSPACE_ID == MIYING_WORKSPACE_ID or BUG_EVALUATION_WORKSPACE_ID == DAOZHEN_WORKSPACE_ID):
                    logger.info(f"缺陷 {bug_id} 已关闭，跳过处理")
                    self.skipped_count += 1
                    update_bug_evaluation_is_closed_without_push(self.workspace_id, bug_id, True)
                    return True
                # 执行BUG规范性评估
                evaluated_data = await check_bug_data(full_bug_data, self.workspace_id)

                if evaluated_data:
                    # 保存评估结果
                    await save_data(evaluated_data)

                    # 统计结果
                    if evaluated_data.get("passed", True):
                        self.passed_count += 1
                    else:
                        self.not_passed_count += 1

                    logger.info(f"缺陷 {bug_id} 评估完成，通过状态: {evaluated_data.get('passed', False)}")
                else:
                    logger.info(f"缺陷 {bug_id} 跳过处理（可能是接口自动化缺陷）")
                    self.skipped_count += 1

                return True

            except Exception as e:
                logger.error(f"处理缺陷失败 {bug_id} (尝试 {attempt + 1}/{self.retry_count + 1}): {str(e)}")
                if attempt < self.retry_count:
                    logger.info(f"等待 {2 ** attempt} 秒后重试...")
                    time.sleep(2 ** attempt)  # 指数退避
                    continue
                return False

        return False

    def _log_progress(self, current: int, total: int):
        """记录处理进度"""
        percentage = (current / total) * 100
        logger.info(f"进度: {current}/{total} ({percentage:.1f}%) - "
                    f"成功: {self.success_count}, 失败: {self.failed_count}, "
                    f"通过: {self.passed_count}, 未通过: {self.not_passed_count}")

    def _log_failed_bugs(self):
        """记录失败的缺陷信息"""
        if self.failed_bugs:
            logger.error("以下缺陷处理失败:")
            for bug_info in self.failed_bugs:
                logger.error(f"  - [{bug_info['index']}] {bug_info['bug_id']}: {bug_info['bug_title']}")
        else:
            logger.info("所有缺陷处理成功")

    def _get_summary(self) -> Dict:
        """获取处理结果摘要"""
        end_time = datetime.now()
        duration = (end_time - self.start_time).total_seconds() if self.start_time else 0

        # 记录失败的缺陷
        self._log_failed_bugs()

        summary = {
            "总处理数量": self.processed_count,
            "成功处理数量": self.success_count,
            "失败数量": self.failed_count,
            "跳过数量": self.skipped_count,
            "规范通过数量": self.passed_count,
            "规范未通过数量": self.not_passed_count,
            "处理耗时": f"{duration:.2f}秒",
            "平均处理时间": f"{duration / max(self.processed_count, 1):.2f}秒/个" if self.processed_count > 0 else "0秒/个",
            "开始时间": self.start_time.strftime("%Y-%m-%d %H:%M:%S") if self.start_time else None,
            "结束时间": end_time.strftime("%Y-%m-%d %H:%M:%S"),
            "工作区ID": self.workspace_id,
            "是否中断": self.interrupted,
            "失败缺陷数量": len(self.failed_bugs),
            "群聊通知数量": self.notified_count,
            "运行模式": "仅群聊通知" if self.notify_only else "评估+通知"
        }

        return summary

    def _log_summary(self, summary: Dict):
        """记录处理结果摘要"""
        logger.info("=" * 50)
        logger.info("BUG主动拉取评估任务完成")
        logger.info("=" * 50)
        for key, value in summary.items():
            logger.info(f"{key}: {value}")
        logger.info("=" * 50)


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="BUG规范性校验主动拉取脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  %(prog)s --start-time "2024-01-01" --end-time "2024-01-02"
  %(prog)s --start-time "2024-01-01 09:00:00" --end-time "2024-01-01 18:00:00"
  %(prog)s --yesterday  # 处理昨天的缺陷
  %(prog)s --notify-only --start-time "2024-01-01" --end-time "2024-01-02"  # 仅发送群聊通知
  %(prog)s --notify-only --yesterday  # 发送昨天未通过BUG的群聊通知
        """
    )

    # 时间参数
    time_group = parser.add_mutually_exclusive_group(required=True)
    time_group.add_argument(
        "--past-days",
        type=int,
        help="处理过去多少天的缺陷，例如 --past-days 7 表示处理过去7天的缺陷"
    )
    time_group.add_argument(
        "--start-time",
        help="开始时间，格式：'2024-01-01' 或 '2024-01-01 09:00:00'"
    )
    time_group.add_argument(
        "--yesterday",
        action="store_true",
        help="处理昨天的缺陷（等同于设置昨天的开始和结束时间）"
    )
    time_group.add_argument(
        "--today",
        action="store_true",
        help="处理今天的缺陷（从今天00:00:00到当前时间）"
    )

    parser.add_argument(
        "--end-time",
        help="结束时间，格式：'2024-01-01' 或 '2024-01-01 18:00:00'，不指定则到当前时间"
    )

    # 其他参数
    parser.add_argument(
        "--workspace-id",
        help="工作区ID，不指定则使用配置文件中的默认值"
    )

    parser.add_argument(
        "--max-bugs",
        type=int,
        help="最大处理缺陷数量，不指定则处理所有符合条件的缺陷"
    )

    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="试运行模式，只获取缺陷列表不执行评估"
    )

    parser.add_argument(
        "--retry-count",
        type=int,
        default=3,
        help="失败重试次数，默认3次"
    )

    parser.add_argument(
        "--batch-size",
        type=int,
        default=10,
        help="批处理大小，每批处理完后暂停，默认10个"
    )

    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="DEBUG",
        help="日志级别，默认INFO"
    )

    parser.add_argument(
        "--skip-mode",
        choices=["exists", "passed"],
        help="跳过模式：'exists'=记录存在则跳过，'passed'=记录存在且通过则跳过，不指定则使用配置文件中的默认值"
    )

    parser.add_argument(
        "--notify-only",
        action="store_true",
        help="仅发送群聊通知模式，查询数据库中指定时间范围内未通过的BUG并发送群聊通知，不执行评估"
    )

    parser.add_argument(
        "--evaluate-bug",
        action="store_true",
        help="拉取BUG进行评估"
    )

    return parser.parse_args()


async def main():
    """主函数"""
    args = parse_arguments()

    try:
        # 设置日志级别
        import logging
        logging.getLogger().setLevel(getattr(logging, args.log_level))
        logger.setLevel(getattr(logging, args.log_level))

        # 处理时间参数
        if args.past_days:
            end = datetime.now()
            start = end - timedelta(days=args.past_days)
            start_time = start.strftime("%Y-%m-%d 00:00:00")
            end_time = end.strftime("%Y-%m-%d %H:%M:%S")  # 使用当前时间作为结束时间
        elif args.yesterday:
            yesterday = datetime.now() - timedelta(days=1)
            start_time = yesterday.strftime("%Y-%m-%d 00:00:00")
            end_time = yesterday.strftime("%Y-%m-%d 23:59:59")
        elif args.today:
            today = datetime.now()
            start_time = today.strftime("%Y-%m-%d 00:00:00")
            end_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        else:
            start_time = args.start_time
            end_time = args.end_time

        # 创建评估器
        evaluator = BugPullEvaluator(
            workspace_id=args.workspace_id,
            retry_count=args.retry_count,
            batch_size=args.batch_size,
            skip_mode=args.skip_mode,
            notify_only=args.notify_only,
            evaluate_bug=args.evaluate_bug
        )

        if args.dry_run:
            # 试运行模式：只获取缺陷列表
            logger.info("试运行模式：只获取缺陷列表，不执行评估")
            bugs_data = tap_client.get_all_bugs_by_time_range(
                workspace_id=evaluator.workspace_id,
                start_time=start_time,
                end_time=end_time,
                max_bugs=args.max_bugs
            )
            logger.info(f"找到 {len(bugs_data)} 个符合条件的缺陷")
            for i, bug_item in enumerate(bugs_data[:10], 1):  # 只显示前10个
                raw_bug = bug_item.get("Bug", {})
                logger.info(f"  {i}. {raw_bug.get('id')} - {raw_bug.get('title', '无标题')}")
            if len(bugs_data) > 10:
                logger.info(f"  ... 还有 {len(bugs_data) - 10} 个缺陷")
        else:
            # 正常模式：执行完整评估
            summary = await evaluator.pull_and_evaluate(
                start_time=start_time,
                end_time=end_time,
                max_bugs=args.max_bugs
            )

            # 输出JSON格式的结果（便于流水线解析）
            print(json.dumps(summary, ensure_ascii=False, indent=2))

    except KeyboardInterrupt:
        logger.info("用户中断执行")
        sys.exit(1)
    except Exception as e:
        logger.error(f"脚本执行失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
